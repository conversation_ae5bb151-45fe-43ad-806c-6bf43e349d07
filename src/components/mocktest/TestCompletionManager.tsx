import { useState, useEffect } from 'react';
import { 
  testCompletionService, 
  CompletedTestNotification 
} from '@/utils/testCompletionService';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { TestCompletionNotifications } from './TestCompletionNotifications';
import { TestResultEntryDialog } from './TestResultEntryDialog';
import { upcomingTestStorage } from '@/utils/mockTestLocalStorage';
import { UpcomingTest } from '@/types/mockTest';
import { toast } from '@/components/ui/use-toast';

interface TestCompletionManagerProps {
  onTestStatusChanged?: () => void;
}

/**
 * Manager component for test completion workflow
 * Handles notifications, result entry, and status transitions
 */
export function TestCompletionManager({ onTestStatusChanged }: TestCompletionManagerProps) {
  const { user } = useSupabaseAuth();
  const [isResultEntryOpen, setIsResultEntryOpen] = useState(false);
  const [selectedTest, setSelectedTest] = useState<UpcomingTest | null>(null);

  // Run daily check on component mount
  useEffect(() => {
    if (user?.id) {
      const notifications = testCompletionService.runDailyCheck(user.id);
      
      // Show toast if there are pending tests
      if (notifications.length > 0) {
        toast({
          title: `${notifications.length} test${notifications.length > 1 ? 's' : ''} awaiting results`,
          description: "Please enter your test results to keep your analytics up to date.",
        });
      }
    }
  }, [user?.id]);

  const handleTestResultEntry = (testId: string) => {
    if (!user?.id) return;
    
    const test = upcomingTestStorage.getById(user.id, testId);
    if (test) {
      setSelectedTest(test);
      setIsResultEntryOpen(true);
    }
  };

  const handleMarkAsMissed = (testId: string) => {
    if (!user?.id) return;
    
    try {
      testCompletionService.markTestAsMissed(user.id, testId);
      
      toast({
        title: "Test marked as missed",
        description: "The test has been removed from your upcoming tests.",
      });
      
      if (onTestStatusChanged) {
        onTestStatusChanged();
      }
    } catch (error) {
      console.error("Error marking test as missed:", error);
      toast({
        title: "Error",
        description: "Failed to mark test as missed",
        variant: "destructive",
      });
    }
  };

  const handleResultsEntered = () => {
    if (onTestStatusChanged) {
      onTestStatusChanged();
    }
  };

  return (
    <>
      <TestCompletionNotifications
        onTestResultEntry={handleTestResultEntry}
        onMarkAsMissed={handleMarkAsMissed}
      />
      
      <TestResultEntryDialog
        open={isResultEntryOpen}
        onOpenChange={setIsResultEntryOpen}
        upcomingTest={selectedTest}
        onResultsEntered={handleResultsEntered}
      />
    </>
  );
}
