import { useState, useEffect } from "react";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { useDocumentTitle } from "@/hooks/useDocumentTitle";
import { useToast } from "@/components/ui/use-toast";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, FileText, BarChart3, Calendar, Target } from "lucide-react";
import { MockTest, MockTestAnalytics, TestCategory, UpcomingTest } from "@/types/mockTest";
import {
  getUserMockTests,
  deleteMockTest,
  generateMockTestAnalytics,
} from "@/utils/mockTestUtils";
import {
  enhancedMockTestUtils,
  categoryStorage
} from "@/utils/mockTestLocalStorage";
import { AddMockTestButton } from "@/components/mocktest/AddMockTestButton";
import { MockTestCard } from "@/components/mocktest/MockTestCard";
import { EditMockTestDialog } from "@/components/mocktest/EditMockTestDialog";
import { MockTestAnalyticsCards } from "@/components/mocktest/MockTestAnalyticsCards";
import { MockTestCharts } from "@/components/mocktest/MockTestCharts";
import { UpcomingTestsDashboard } from "@/components/mocktest/UpcomingTestsDashboard";
import { TestManagementHub } from "@/components/mocktest/TestManagementHub";
import { CategoryManager } from "@/components/mocktest/CategoryManager";
import { PerformanceTrendAnalytics } from "@/components/mocktest/PerformanceTrendAnalytics";
import { EnhancedScoreTable } from "@/components/mocktest/EnhancedScoreTable";
import { TestBreakdownModal } from "@/components/mocktest/TestBreakdownModal";
import { SubjectPerformanceChart } from "@/components/mocktest/SubjectPerformanceChart";
import { CategoryAnalyticsChart } from "@/components/mocktest/CategoryAnalyticsChart";
import { TimeAnalyticsChart } from "@/components/mocktest/TimeAnalyticsChart";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Header } from "@/components/shared";
import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "framer-motion";

export default function MockTestAnalysis() {
  useDocumentTitle("Mock Test Analysis - IsotopeAI");
  const { user } = useSupabaseAuth();
  const { toast } = useToast();

  const [mockTests, setMockTests] = useState<MockTest[]>([]);
  const [enhancedTests, setEnhancedTests] = useState<MockTest[]>([]);
  const [filteredTests, setFilteredTests] = useState<MockTest[]>([]);
  const [mockTestAnalytics, setMockTestAnalytics] = useState<MockTestAnalytics | null>(null);
  const [categories, setCategories] = useState<TestCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedSubject, setSelectedSubject] = useState<string>("all");
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState<string>("all");
  const [reviewFilter, setReviewFilter] = useState<string>("all");
  const [sortOption, setSortOption] = useState<string>("date-desc");

  const [editingTest, setEditingTest] = useState<MockTest | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [deletingTestId, setDeletingTestId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedTestForDetails, setSelectedTestForDetails] = useState<MockTest | null>(null);
  const [isTestDetailsModalOpen, setIsTestDetailsModalOpen] = useState(false);

  // Get all available subjects from mock tests
  const availableSubjects = [...new Set(enhancedTests.flatMap((test) => test.subjectMarks.map((sm) => sm.subject)))];

  // Load mock tests, categories, and analytics
  useEffect(() => {
    const loadMockTests = async () => {
      if (!user) return;

      setIsLoading(true);
      try {
        const basicTests = await getUserMockTests(user.id);
        setMockTests(basicTests);

        // Merge with enhanced local data
        const enhanced = enhancedMockTestUtils.mergeMultipleWithLocalData(basicTests);
        setEnhancedTests(enhanced);
        setFilteredTests(enhanced);

        // Load categories
        const cats = categoryStorage.getAll(user.id);
        setCategories(cats);

        const analytics = generateMockTestAnalytics(enhanced);
        setMockTestAnalytics(analytics);
      } catch (error) {
        console.error("Error loading mock tests:", error);
        toast({
          title: "Error",
          description: "Failed to load mock tests",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadMockTests();
  }, [user, toast]);

  // Filter and sort tests based on search query, subject, category, review status, and sort option
  useEffect(() => {
    if (!enhancedTests.length) {
      setFilteredTests([]);
      return;
    }

    let filtered = [...enhancedTests];

    // Filter by search query (name or notes)
    if (searchQuery) {
      filtered = filtered.filter((test) =>
        test.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        test.notes?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by subject
    if (selectedSubject !== "all") {
      filtered = filtered.filter((test) =>
        test.subjectMarks.some((sm) => sm.subject === selectedSubject)
      );
    }

    // Filter by category
    if (selectedCategoryFilter !== "all") {
      if (selectedCategoryFilter === "uncategorized") {
        filtered = filtered.filter((test) => !test.categoryId);
      } else {
        filtered = filtered.filter((test) => test.categoryId === selectedCategoryFilter);
      }
    }

    // Filter by review status
    if (reviewFilter !== "all") {
      if (reviewFilter === "reviewed") {
        filtered = filtered.filter((test) => test.isReviewed);
      } else {
        filtered = filtered.filter((test) => !test.isReviewed);
      }
    }

    // Sort tests
    switch (sortOption) {
      case "date-asc":
        filtered.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        break;
      case "date-desc":
        filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
        break;
      case "score-asc":
        filtered.sort((a, b) => {
          const aScore = a.marksObtained && a.totalMarks ? a.marksObtained / a.totalMarks : 0;
          const bScore = b.marksObtained && b.totalMarks ? b.marksObtained / b.totalMarks : 0;
          return aScore - bScore;
        });
        break;
      case "score-desc":
        filtered.sort((a, b) => {
          const aScore = a.marksObtained && a.totalMarks ? a.marksObtained / a.totalMarks : 0;
          const bScore = b.marksObtained && b.totalMarks ? b.marksObtained / b.totalMarks : 0;
          return bScore - aScore;
        });
        break;
      default:
        filtered.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    }

    setFilteredTests(filtered);
  }, [enhancedTests, searchQuery, selectedSubject, selectedCategoryFilter, reviewFilter, sortOption]);

  // Handle deleting a mock test
  const handleDeleteTest = async () => {
    if (!user || !deletingTestId) return;

    try {
      const success = await deleteMockTest(user.id, deletingTestId);

      if (success) {
        // Also clean up local enhanced data
        enhancedMockTestUtils.deleteEnhancedData(deletingTestId);

        const updatedTests = mockTests.filter((test) => test.id !== deletingTestId);
        setMockTests(updatedTests);

        const enhanced = enhancedMockTestUtils.mergeMultipleWithLocalData(updatedTests);
        setEnhancedTests(enhanced);

        const analytics = generateMockTestAnalytics(enhanced);
        setMockTestAnalytics(analytics);

        toast({
          title: "Success",
          description: "Mock test deleted successfully",
        });
      }
    } catch (error) {
      console.error("Error deleting mock test:", error);
      toast({
        title: "Error",
        description: "Failed to delete mock test",
        variant: "destructive",
      });
    } finally {
      setDeletingTestId(null);
      setIsDeleteDialogOpen(false);
    }
  };

  // Handle refreshing mock tests
  const handleRefreshTests = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const basicTests = await getUserMockTests(user.id);
      setMockTests(basicTests);

      const enhanced = enhancedMockTestUtils.mergeMultipleWithLocalData(basicTests);
      setEnhancedTests(enhanced);

      const analytics = generateMockTestAnalytics(enhanced);
      setMockTestAnalytics(analytics);

      toast({
        title: "Success",
        description: "Mock tests refreshed successfully",
      });
    } catch (error) {
      console.error("Error refreshing mock tests:", error);
      toast({
        title: "Error",
        description: "Failed to refresh mock tests",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle test update from modal
  const handleTestUpdate = (updatedTest: MockTest) => {
    setMockTests(prev => prev.map(test => test.id === updatedTest.id ? updatedTest : test));
    setEnhancedTests(prev => prev.map(test => test.id === updatedTest.id ? updatedTest : test));

    // Update analytics
    const analytics = generateMockTestAnalytics(enhancedTests);
    setMockTestAnalytics(analytics);
  };

  // Handle viewing test details
  const handleViewTestDetails = (test: MockTest) => {
    setSelectedTestForDetails(test);
    setIsTestDetailsModalOpen(true);
  };



  // Handle editing a test
  const handleEditTest = (test: MockTest) => {
    setEditingTest(test);
    setIsEditDialogOpen(true);
  };

  // Handle delete click (opens confirmation dialog)
  const handleDeleteClick = (testId: string) => {
    setDeletingTestId(testId);
    setIsDeleteDialogOpen(true);
  };

  if (!user) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Authentication Error</AlertTitle>
          <AlertDescription>
            You need to be logged in to view this page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  };

  return (
    <>
      <Header />
      <div className="relative min-h-screen bg-gradient-to-b from-background to-background/80 pt-20 pb-12">

        <div className="container mx-auto py-8 px-4 sm:px-6">
          {/* Header Section with Gradient Background */}
          <motion.div
            className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-primary/10 via-primary/5 to-background p-6 mb-8 border border-primary/10 shadow-sm"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div className="relative">
                <h1 className="text-4xl font-bold tracking-tight gradient-heading mb-2">Mock Test Analysis</h1>
                <p className="text-muted-foreground text-lg max-w-2xl">
                  Track and analyze your performance in mock tests to improve your results
                </p>
                <div className="absolute -bottom-1 left-0 h-1 w-24 bg-gradient-to-r from-primary to-primary/40 rounded-full"></div>
              </div>
              <div className="relative">
                <AddMockTestButton
                  onAddMockTest={handleRefreshTests}
                  className="bg-primary hover:bg-primary/90 text-white shadow-md"
                />
              </div>
            </div>
          </motion.div>

        <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            <Tabs defaultValue="analytics" className="w-full">
              <TabsList className="mb-6 w-full sm:w-auto p-1 bg-muted/80 backdrop-blur-sm rounded-xl">
                <TabsTrigger
                  value="analytics"
                  className="flex items-center gap-2 px-4 py-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-primary data-[state=active]:text-primary-foreground transition-all duration-300"
                >
                  <BarChart3 className="h-4 w-4" />
                  Analytics
                </TabsTrigger>
                <TabsTrigger
                  value="upcoming"
                  className="flex items-center gap-2 px-4 py-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-primary data-[state=active]:text-primary-foreground transition-all duration-300"
                >
                  <Calendar className="h-4 w-4" />
                  Upcoming Tests
                </TabsTrigger>
                <TabsTrigger
                  value="tests"
                  className="flex items-center gap-2 px-4 py-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-primary data-[state=active]:text-primary-foreground transition-all duration-300"
                >
                  <FileText className="h-4 w-4" />
                  Tests
                </TabsTrigger>
                <TabsTrigger
                  value="categories"
                  className="flex items-center gap-2 px-4 py-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-primary data-[state=active]:text-primary-foreground transition-all duration-300"
                >
                  <Target className="h-4 w-4" />
                  Categories
                </TabsTrigger>
              </TabsList>

          <TabsContent value="analytics">
            {isLoading ? (
              <div className="flex flex-col items-center justify-center h-80 gap-4">
                <div className="relative w-16 h-16">
                  <div className="absolute inset-0 rounded-full border-t-2 border-primary animate-spin"></div>
                  <div className="absolute inset-2 rounded-full border-t-2 border-primary/70 animate-spin" style={{ animationDuration: '1.5s' }}></div>
                  <div className="absolute inset-4 rounded-full border-t-2 border-primary/40 animate-spin" style={{ animationDuration: '2s' }}></div>
                </div>
                <p className="text-muted-foreground animate-pulse">Loading your analytics...</p>
              </div>
            ) : mockTestAnalytics && enhancedTests.length > 0 ? (
              <motion.div
                className="space-y-8"
                variants={containerVariants}
                initial="hidden"
                animate="visible"
              >
                <motion.div variants={itemVariants}>
                  <MockTestAnalyticsCards analytics={mockTestAnalytics} />
                </motion.div>
                <motion.div variants={itemVariants} className="mb-8">
                  <h2 className="text-2xl font-bold mb-6 gradient-heading">Detailed Performance Analysis</h2>
                  <MockTestCharts analytics={mockTestAnalytics} mockTests={enhancedTests} />
                </motion.div>
                <motion.div variants={itemVariants} className="mb-8">
                  <h2 className="text-2xl font-bold mb-6 gradient-heading">Performance Trends</h2>
                  <PerformanceTrendAnalytics tests={enhancedTests} />
                </motion.div>
                <motion.div variants={itemVariants} className="mb-8">
                  <h2 className="text-2xl font-bold mb-6 gradient-heading">Subject Performance</h2>
                  <SubjectPerformanceChart tests={enhancedTests} />
                </motion.div>
                <motion.div variants={itemVariants} className="mb-8">
                  <h2 className="text-2xl font-bold mb-6 gradient-heading">Timeline & Category Analytics</h2>
                  <div className="space-y-8">
                    <TimeAnalyticsChart tests={enhancedTests} />
                    <CategoryAnalyticsChart tests={enhancedTests} categories={categories} />
                  </div>
                </motion.div>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <Alert className="bg-card border border-primary/10 shadow-md">
                  <AlertCircle className="h-5 w-5 text-primary" />
                  <AlertTitle className="text-lg font-semibold">No mock tests found</AlertTitle>
                  <AlertDescription className="text-muted-foreground">
                    Add your first mock test to start tracking your performance and see detailed analytics.
                  </AlertDescription>
                </Alert>
              </motion.div>
            )}
          </TabsContent>

          <TabsContent value="upcoming">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <TestManagementHub />
            </motion.div>
          </TabsContent>

          <TabsContent value="categories">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <CategoryManager />
            </motion.div>
          </TabsContent>

          <TabsContent value="tests">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <div className="flex flex-col gap-4 mb-6">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <Label htmlFor="search-tests" className="sr-only">Search tests</Label>
                    <Input
                      id="search-tests"
                      placeholder="Search tests by name or notes..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <Select value={selectedSubject} onValueChange={setSelectedSubject}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Filter by subject" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Subjects</SelectItem>
                      {availableSubjects.map((subject) => (
                        <SelectItem key={subject} value={subject}>
                          {subject}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Select value={selectedCategoryFilter} onValueChange={setSelectedCategoryFilter}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Filter by category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="uncategorized">Uncategorized</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={reviewFilter} onValueChange={setReviewFilter}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Filter by review status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="reviewed">Reviewed</SelectItem>
                      <SelectItem value="unreviewed">Unreviewed</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={sortOption} onValueChange={setSortOption}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="date-desc">Date (Newest first)</SelectItem>
                      <SelectItem value="date-asc">Date (Oldest first)</SelectItem>
                      <SelectItem value="score-desc">Score (Highest first)</SelectItem>
                      <SelectItem value="score-asc">Score (Lowest first)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Tabs defaultValue="table" className="w-full">
                <TabsList className="mb-6 w-full sm:w-auto p-1 bg-muted/80 backdrop-blur-sm rounded-xl">
                  <TabsTrigger
                    value="table"
                    className="flex items-center gap-2 px-4 py-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-primary data-[state=active]:text-primary-foreground transition-all duration-300"
                  >
                    <FileText className="h-4 w-4" />
                    Table View
                  </TabsTrigger>
                  <TabsTrigger
                    value="cards"
                    className="flex items-center gap-2 px-4 py-2 data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-primary data-[state=active]:text-primary-foreground transition-all duration-300"
                  >
                    <BarChart3 className="h-4 w-4" />
                    Card View
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="table">
                  <EnhancedScoreTable
                    tests={filteredTests}
                    categories={categories}
                    onViewDetails={handleViewTestDetails}
                  />
                </TabsContent>

                <TabsContent value="cards">
                  {filteredTests.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-12">
                      <FileText className="h-16 w-16 text-muted-foreground mb-4" />
                      <h3 className="text-xl font-semibold mb-2">No tests found</h3>
                      <p className="text-muted-foreground text-center">
                        Adjust your filters or add your first mock test to see it displayed here.
                      </p>
                    </div>
                  ) : (
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                      {filteredTests.map((test, index) => (
                        <motion.div
                          key={test.id}
                          variants={itemVariants}
                          custom={index}
                        >
                          <MockTestCard
                            mockTest={test}
                            onEditClick={handleEditTest}
                            onDeleteClick={handleDeleteClick}
                            onViewDetails={handleViewTestDetails}
                            categories={categories}
                          />
                        </motion.div>
                      ))}
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </motion.div>
          </TabsContent>
        </Tabs>
        </motion.div>
        </div>

        {/* Test Breakdown Modal */}
        <TestBreakdownModal
          test={selectedTestForDetails}
          isOpen={isTestDetailsModalOpen}
          onClose={() => {
            setIsTestDetailsModalOpen(false);
            setSelectedTestForDetails(null);
          }}
          onTestUpdate={handleTestUpdate}
        />

        {/* Edit Test Dialog */}
        <EditMockTestDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          mockTest={editingTest}
          userId={user.id}
          onSave={handleRefreshTests}
        />

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent className="border border-destructive/20">
            <AlertDialogHeader>
              <AlertDialogTitle className="text-xl">Confirm Deletion</AlertDialogTitle>
              <AlertDialogDescription className="text-muted-foreground">
                Are you sure you want to delete this mock test? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="gap-2">
              <AlertDialogCancel className="border-primary/20">Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteTest}
                className="bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
}
